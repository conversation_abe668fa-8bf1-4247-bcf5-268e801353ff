import { 
  AIProvider, 
  AIProviderType, 
  AIProviderConfig,
  AIGenerationRequest,
  AIGenerationResponse,
  AITestConnectionResult,
  AI_PROVIDERS
} from './types';
import { OpenAIProvider } from './openaiProvider';
import { AnthropicProvider } from './anthropicProvider';
import { GeminiProvider, CustomProvider } from './geminiProvider';

// AI生成请求（兼容原有接口）
export interface MandalaAIRequest {
  parentContent: string;
  level: number;
  position: number;
  context: string[];
}

export class AIServiceManager {
  private currentProvider: AIProvider | null = null;
  private config: AIProviderConfig = {
    apiKey: '',
    baseURL: '',
    model: '',
    temperature: 0.7,
    maxTokens: 500
  };

  constructor() {
    // 默认使用OpenAI
    this.setProvider(AIProviderType.OPENAI);
  }

  // 设置AI服务提供商
  setProvider(providerType: AIProviderType) {
    const providerInfo = AI_PROVIDERS[providerType];
    
    // 更新配置
    this.config.baseURL = providerInfo.baseURL;
    if (providerInfo.models.length > 0) {
      this.config.model = providerInfo.models[0];
    }

    // 创建对应的Provider实例
    switch (providerType) {
      case AIProviderType.OPENAI:
        this.currentProvider = new OpenAIProvider(this.config);
        break;
      case AIProviderType.ANTHROPIC:
        this.currentProvider = new AnthropicProvider(this.config);
        break;
      case AIProviderType.GEMINI:
        this.currentProvider = new GeminiProvider(this.config);
        break;
      case AIProviderType.CUSTOM:
        this.currentProvider = new CustomProvider(this.config);
        break;
      default:
        throw new Error(`不支持的AI服务提供商: ${providerType}`);
    }
  }

  // 更新配置
  updateConfig(newConfig: Partial<AIProviderConfig>) {
    this.config = { ...this.config, ...newConfig };
    if (this.currentProvider) {
      this.currentProvider.updateConfig(this.config);
    }
  }

  // 获取当前配置
  getConfig(): AIProviderConfig {
    return { ...this.config };
  }

  // 获取当前提供商
  getCurrentProvider(): AIProvider | null {
    return this.currentProvider;
  }

  // 获取支持的模型列表
  getSupportedModels(): string[] {
    return this.currentProvider?.getSupportedModels() || [];
  }

  // 测试连接
  async testConnection(): Promise<AITestConnectionResult> {
    if (!this.currentProvider) {
      return {
        success: false,
        message: '未设置AI服务提供商'
      };
    }

    return this.currentProvider.testConnection();
  }

  // 生成单个内容（兼容原有接口）
  async generateSingleContent(request: MandalaAIRequest): Promise<string> {
    if (!this.currentProvider) {
      throw new Error('未设置AI服务提供商');
    }

    const prompt = this.buildPrompt(request);
    const aiRequest: AIGenerationRequest = {
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens
    };

    const response = await this.currentProvider.generateResponse(aiRequest);
    return response.content;
  }

  // 批量生成内容（兼容原有接口）
  async generateBatchContent(request: MandalaAIRequest): Promise<string[]> {
    if (!this.currentProvider) {
      throw new Error('未设置AI服务提供商');
    }

    const prompt = this.buildBatchPrompt(request);
    const aiRequest: AIGenerationRequest = {
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens
    };

    const response = await this.currentProvider.generateResponse(aiRequest);
    
    // 解析批量响应
    const lines = response.content.split('\n').filter(line => line.trim());
    const items: string[] = [];
    
    for (const line of lines) {
      const match = line.match(/^\d+\.\s*(.+)$/);
      if (match) {
        items.push(match[1].trim());
      } else if (line.trim() && !line.includes('以下是') && !line.includes('围绕')) {
        items.push(line.trim());
      }
    }
    
    // 确保返回8个项目
    while (items.length < 8) {
      items.push(`相关项目 ${items.length + 1}`);
    }
    
    return items.slice(0, 8);
  }

  // 构建单个内容生成的提示词
  private buildPrompt(request: MandalaAIRequest): string {
    const { parentContent, level, context } = request;
    
    if (level === 1) {
      return `请为曼陀罗图的中心主题"${parentContent}"生成一个相关的子主题。要求：
1. 与主题密切相关
2. 具体且可操作
3. 简洁明了（不超过10个字）
4. 避免重复已有内容：${context.join('、')}

请直接返回子主题名称，不要添加序号或其他说明。`;
    } else {
      return `请为曼陀罗图的子主题"${parentContent}"生成一个具体的行动项或细分内容。要求：
1. 与子主题直接相关
2. 具体可执行
3. 简洁明了（不超过15个字）
4. 避免重复已有内容：${context.join('、')}

请直接返回行动项名称，不要添加序号或其他说明。`;
    }
  }

  // 构建批量内容生成的提示词
  private buildBatchPrompt(request: MandalaAIRequest): string {
    const { parentContent, level, context } = request;
    
    if (level === 1) {
      return `请为曼陀罗图的中心主题"${parentContent}"生成8个相关的子主题。要求：
1. 每个子主题都与中心主题密切相关
2. 涵盖主题的不同方面
3. 具体且可操作
4. 简洁明了（每个不超过10个字）
5. 避免重复已有内容：${context.join('、')}

请按以下格式返回：
1. 子主题1
2. 子主题2
3. 子主题3
4. 子主题4
5. 子主题5
6. 子主题6
7. 子主题7
8. 子主题8`;
    } else {
      return `请为曼陀罗图的子主题"${parentContent}"生成8个具体的行动项或细分内容。要求：
1. 每个行动项都与子主题直接相关
2. 具体可执行
3. 简洁明了（每个不超过15个字）
4. 涵盖子主题的不同方面
5. 避免重复已有内容：${context.join('、')}

请按以下格式返回：
1. 行动项1
2. 行动项2
3. 行动项3
4. 行动项4
5. 行动项5
6. 行动项6
7. 行动项7
8. 行动项8`;
    }
  }
}

// 导出单例实例
export const aiServiceManager = new AIServiceManager();
