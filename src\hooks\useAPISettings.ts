import { useState, useEffect, useCallback } from 'react';
import { APISettings, AIProvider, GeminiModel, APIKeyValidationResult } from '../types/mandala';
import { aiService } from '../services/aiService';
import { storageService } from '../services/storageService';

export const useAPISettings = () => {
  const [settings, setSettings] = useState<APISettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载设置
  const loadSettings = useCallback(async () => {
    try {
      setIsLoading(true);
      const savedSettings = storageService.getAPISettings();
      setSettings(savedSettings);
      
      // 更新 AI 服务的设置
      aiService.setAPISettings(savedSettings);
    } catch (err) {
      console.error('加载 API 设置失败:', err);
      setError(err instanceof Error ? err.message : '加载设置失败');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 保存设置
  const saveSettings = useCallback(async (newSettings: APISettings): Promise<boolean> => {
    try {
      setError(null);
      const success = storageService.saveAPISettings(newSettings);
      
      if (success) {
        setSettings(newSettings);
        aiService.setAPISettings(newSettings);
        return true;
      } else {
        setError('保存设置失败');
        return false;
      }
    } catch (err) {
      console.error('保存 API 设置失败:', err);
      setError(err instanceof Error ? err.message : '保存设置失败');
      return false;
    }
  }, []);

  // 验证 API 密钥
  const validateAPIKey = useCallback(async (
    provider: AIProvider, 
    apiKey: string
  ): Promise<APIKeyValidationResult> => {
    try {
      setError(null);
      return await aiService.validateAPIKey(provider, apiKey);
    } catch (err) {
      console.error('验证 API 密钥失败:', err);
      const errorMessage = err instanceof Error ? err.message : '验证失败';
      setError(errorMessage);
      return { isValid: false, error: errorMessage };
    }
  }, []);

  // 获取可用模型
  const getAvailableModels = useCallback(async (provider: AIProvider): Promise<GeminiModel[]> => {
    try {
      setError(null);
      return await aiService.getAvailableModels(provider);
    } catch (err) {
      console.error('获取模型列表失败:', err);
      setError(err instanceof Error ? err.message : '获取模型列表失败');
      return [];
    }
  }, []);

  // 更新提供商
  const updateProvider = useCallback(async (provider: AIProvider): Promise<boolean> => {
    if (!settings) return false;
    
    const newSettings: APISettings = {
      ...settings,
      provider,
    };
    
    return await saveSettings(newSettings);
  }, [settings, saveSettings]);

  // 更新 Gemini 设置
  const updateGeminiSettings = useCallback(async (geminiSettings: {
    apiKey?: string;
    model?: string;
    availableModels?: GeminiModel[];
  }): Promise<boolean> => {
    if (!settings || settings.provider !== 'gemini') return false;
    
    const newSettings: APISettings = {
      ...settings,
      gemini: {
        ...settings.gemini,
        ...geminiSettings,
      },
    };
    
    return await saveSettings(newSettings);
  }, [settings, saveSettings]);

  // 更新生成参数
  const updateGenerationParams = useCallback(async (params: {
    maxTokens?: number;
    temperature?: number;
  }): Promise<boolean> => {
    if (!settings) return false;
    
    const newSettings: APISettings = {
      ...settings,
      ...params,
    };
    
    return await saveSettings(newSettings);
  }, [settings, saveSettings]);

  // 清除设置
  const clearSettings = useCallback(async (): Promise<boolean> => {
    try {
      setError(null);
      const success = storageService.clearAPISettings();
      
      if (success) {
        // 重新加载默认设置
        await loadSettings();
        return true;
      } else {
        setError('清除设置失败');
        return false;
      }
    } catch (err) {
      console.error('清除 API 设置失败:', err);
      setError(err instanceof Error ? err.message : '清除设置失败');
      return false;
    }
  }, [loadSettings]);

  // 检查设置是否完整
  const isSettingsComplete = useCallback((): boolean => {
    if (!settings) return false;
    
    switch (settings.provider) {
      case 'gemini':
        return !!(settings.gemini?.apiKey && settings.gemini?.model);
      case 'openai':
        return !!(settings.openai?.apiKey && settings.openai?.model);
      default:
        return false;
    }
  }, [settings]);

  // 获取当前 API 密钥
  const getCurrentAPIKey = useCallback((): string => {
    if (!settings) return '';
    
    switch (settings.provider) {
      case 'gemini':
        return settings.gemini?.apiKey || '';
      case 'openai':
        return settings.openai?.apiKey || '';
      default:
        return '';
    }
  }, [settings]);

  // 获取当前模型
  const getCurrentModel = useCallback((): string => {
    if (!settings) return '';
    
    switch (settings.provider) {
      case 'gemini':
        return settings.gemini?.model || '';
      case 'openai':
        return settings.openai?.model || '';
      default:
        return '';
    }
  }, [settings]);

  // 检查是否需要刷新模型列表
  const shouldRefreshModels = useCallback((): boolean => {
    if (!settings || settings.provider !== 'gemini' || !settings.gemini) return false;
    
    const lastFetch = settings.gemini.lastModelsFetch;
    if (!lastFetch) return true;
    
    // 如果超过1小时，建议刷新
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    return new Date(lastFetch) < oneHourAgo;
  }, [settings]);

  // 初始化加载
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  return {
    // 状态
    settings,
    isLoading,
    error,
    
    // 操作方法
    loadSettings,
    saveSettings,
    validateAPIKey,
    getAvailableModels,
    updateProvider,
    updateGeminiSettings,
    updateGenerationParams,
    clearSettings,
    
    // 辅助方法
    isSettingsComplete,
    getCurrentAPIKey,
    getCurrentModel,
    shouldRefreshModels,
  };
};
