import { useEffect, useRef, useState } from 'react';
import { useMandala } from '../contexts/MandalaContext';
import { storageService } from '../services/storageService';
import { aiServiceManager } from '../services/aiProviders/aiServiceManager';
import { AIProviderType } from '../services/aiProviders/types';
import { MandalaChart } from '../types/mandala';

export const usePersistence = () => {
  const { state, dispatch } = useMandala();
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [lastSaveTime, setLastSaveTime] = useState<Date | null>(null);
  const autoSaveIntervalRef = useRef<number | null>(null);
  
  // 初始化时加载数据
  useEffect(() => {
    const loadInitialData = () => {
      // 加载应用设置
      const settings = storageService.getSettings();
      setAutoSaveEnabled(settings.autoSave);

      // 初始化AI服务配置
      if (settings.aiProvider) {
        aiServiceManager.setProvider(settings.aiProvider as AIProviderType);
      }

      aiServiceManager.updateConfig({
        apiKey: settings.aiApiKey || '',
        baseURL: settings.aiBaseURL || '',
        model: settings.aiModel || '',
        temperature: settings.aiTemperature ?? 0.7,
        maxTokens: settings.aiMaxTokens ?? 500
      });

      // 加载当前图表
      const currentChart = storageService.getCurrentChart();
      if (currentChart) {
        dispatch({ type: 'LOAD_CHART', payload: currentChart });
      }
    };

    loadInitialData();
  }, [dispatch]);
  
  // 自动保存功能
  useEffect(() => {
    if (!state.currentChart || !autoSaveEnabled) {
      if (autoSaveIntervalRef.current) {
        clearInterval(autoSaveIntervalRef.current);
        autoSaveIntervalRef.current = null;
      }
      return;
    }
    
    const settings = storageService.getSettings();
    const interval = settings.autoSaveInterval * 1000;
    
    // 清除之前的定时器
    if (autoSaveIntervalRef.current) {
      clearInterval(autoSaveIntervalRef.current);
    }
    
    // 设置新的定时器
    autoSaveIntervalRef.current = window.setInterval(() => {
      if (state.currentChart) {
        const success = storageService.saveChart(state.currentChart);
        if (success) {
          setLastSaveTime(new Date());
        }
      }
    }, interval);
    
    return () => {
      if (autoSaveIntervalRef.current) {
        clearInterval(autoSaveIntervalRef.current);
      }
    };
  }, [state.currentChart, autoSaveEnabled]);
  
  // 手动保存
  const saveChart = (chart?: MandalaChart): boolean => {
    const chartToSave = chart || state.currentChart;
    if (!chartToSave) return false;
    
    const success = storageService.saveChart(chartToSave);
    if (success) {
      setLastSaveTime(new Date());
    }
    return success;
  };
  
  // 加载图表
  const loadChart = (chartId: string): boolean => {
    const chart = storageService.getChartById(chartId);
    if (chart) {
      dispatch({ type: 'LOAD_CHART', payload: chart });
      return true;
    }
    return false;
  };
  
  // 获取所有图表
  const getAllCharts = (): MandalaChart[] => {
    return storageService.getAllCharts();
  };
  
  // 删除图表
  const deleteChart = (chartId: string): boolean => {
    const success = storageService.deleteChart(chartId);
    if (success && state.currentChart?.id === chartId) {
      // 如果删除的是当前图表，清除当前状态
      dispatch({ type: 'LOAD_CHART', payload: null as any });
    }
    return success;
  };
  
  // 导出数据
  const exportData = (): string => {
    return storageService.exportData();
  };
  
  // 导入数据
  const importData = (jsonData: string): boolean => {
    const success = storageService.importData(jsonData);
    if (success) {
      // 重新加载当前图表
      const currentChart = storageService.getCurrentChart();
      if (currentChart) {
        dispatch({ type: 'LOAD_CHART', payload: currentChart });
      }
    }
    return success;
  };
  
  // 切换自动保存
  const toggleAutoSave = (): void => {
    const newState = !autoSaveEnabled;
    setAutoSaveEnabled(newState);
    storageService.saveSettings({ autoSave: newState });
  };
  
  // 设置自动保存间隔
  const setAutoSaveInterval = (seconds: number): void => {
    storageService.saveSettings({ autoSaveInterval: seconds });
  };
  
  // 获取存储信息
  const getStorageInfo = () => {
    return storageService.getStorageInfo();
  };
  
  // 清除所有数据
  const clearAllData = (): boolean => {
    const success = storageService.clearAllData();
    if (success) {
      dispatch({ type: 'LOAD_CHART', payload: null as any });
      setLastSaveTime(null);
    }
    return success;
  };
  
  // 格式化最后保存时间
  const getLastSaveTimeText = (): string => {
    if (!lastSaveTime) return '未保存';
    
    const now = new Date();
    const diff = now.getTime() - lastSaveTime.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (seconds < 60) {
      return '刚刚保存';
    } else if (minutes < 60) {
      return `${minutes}分钟前保存`;
    } else if (hours < 24) {
      return `${hours}小时前保存`;
    } else {
      return lastSaveTime.toLocaleDateString();
    }
  };
  
  return {
    // 状态
    autoSaveEnabled,
    lastSaveTime,
    lastSaveTimeText: getLastSaveTimeText(),
    
    // 操作
    saveChart,
    loadChart,
    getAllCharts,
    deleteChart,
    exportData,
    importData,
    toggleAutoSave,
    setAutoSaveInterval,
    getStorageInfo,
    clearAllData,
  };
};
