import { 
  AIProvider, 
  AIGenerationRequest, 
  AIGenerationResponse, 
  AITestConnectionResult,
  AIProviderConfig 
} from './types';

// OpenAI API请求格式
interface OpenAIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

interface OpenAIRequest {
  model: string;
  messages: OpenAIMessage[];
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
}

interface OpenAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface OpenAIErrorResponse {
  error: {
    message: string;
    type: string;
    code?: string;
  };
}

export class OpenAIProvider extends AIProvider {
  constructor(config: AIProviderConfig) {
    super(config);
  }

  getProviderName(): string {
    return 'OpenAI';
  }

  getSupportedModels(): string[] {
    return ['gpt-4', 'gpt-4-turbo', 'gpt-4o', 'gpt-3.5-turbo', 'gpt-4o-mini'];
  }

  async testConnection(): Promise<AITestConnectionResult> {
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${this.config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        body: JSON.stringify({
          model: this.config.model,
          messages: [
            {
              role: 'user',
              content: '测试连接，请回复"连接成功"'
            }
          ],
          max_tokens: 10,
          temperature: 0
        } as OpenAIRequest)
      });

      const latency = Date.now() - startTime;

      if (!response.ok) {
        const errorData: OpenAIErrorResponse = await response.json();
        return {
          success: false,
          message: `API请求失败 (${response.status}): ${errorData.error?.message || response.statusText}`,
          latency
        };
      }

      const data: OpenAIResponse = await response.json();
      
      if (data.choices && data.choices.length > 0) {
        return {
          success: true,
          message: 'OpenAI API连接测试成功！',
          latency
        };
      } else {
        return {
          success: false,
          message: 'API返回格式异常：缺少choices字段',
          latency
        };
      }
    } catch (error) {
      const latency = Date.now() - startTime;
      
      if (error instanceof TypeError && error.message.includes('fetch')) {
        return {
          success: false,
          message: '网络连接失败，请检查URL是否正确',
          latency
        };
      }
      
      return {
        success: false,
        message: `连接失败: ${error instanceof Error ? error.message : '未知错误'}`,
        latency
      };
    }
  }

  async generateResponse(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    if (!this.config.apiKey.trim()) {
      throw new Error('请先配置OpenAI API密钥');
    }

    const openaiRequest: OpenAIRequest = {
      model: request.model || this.config.model,
      messages: request.messages.map(msg => ({
        role: msg.role,
        content: msg.content
      })),
      temperature: request.temperature ?? this.config.temperature,
      max_tokens: request.maxTokens ?? this.config.maxTokens
    };

    try {
      const response = await fetch(`${this.config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        body: JSON.stringify(openaiRequest)
      });

      if (!response.ok) {
        const errorData: OpenAIErrorResponse = await response.json();
        throw new Error(`OpenAI API错误 (${response.status}): ${errorData.error?.message || response.statusText}`);
      }

      const data: OpenAIResponse = await response.json();
      
      if (!data.choices || data.choices.length === 0) {
        throw new Error('OpenAI API返回格式异常：缺少choices字段');
      }

      const choice = data.choices[0];
      
      return {
        content: choice.message.content || '',
        usage: {
          inputTokens: data.usage.prompt_tokens,
          outputTokens: data.usage.completion_tokens,
          totalTokens: data.usage.total_tokens
        },
        finishReason: choice.finish_reason
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`OpenAI API调用失败: ${String(error)}`);
    }
  }
}
