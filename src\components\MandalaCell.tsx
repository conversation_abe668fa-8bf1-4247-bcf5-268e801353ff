import React, { useState, useRef, useEffect } from 'react';
import { Sparkles } from 'lucide-react';
import { MandalaNode, GRID_POSITIONS } from '../types/mandala';
import { useMandala } from '../contexts/MandalaContext';
import { getLevelName, getPositionName } from '../utils/mandala';

interface MandalaCellProps {
  node: MandalaNode;
  onNavigate?: (nodeId: string) => void;
  onAIGenerate?: (nodeId: string) => void;
  className?: string;
  style?: React.CSSProperties;
}

export const MandalaCell: React.FC<MandalaCellProps> = ({
  node,
  onNavigate,
  onAIGenerate,
  className = '',
  style,
}) => {
  const { state, dispatch } = useMandala();
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(node.content);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  const isCenter = node.position === GRID_POSITIONS.CENTER;
  const canNavigate = node.content.trim() !== '' && node.level < (state.currentChart?.maxLevel || 4);
  const showAIButton = state.isEditMode || node.content.trim() === '';
  const [isHovered, setIsHovered] = useState(false);
  
  // 自动调整textarea高度
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [editContent]);
  
  // 进入编辑模式
  const handleStartEdit = () => {
    if (!state.isEditMode || !node.isEditable) return;
    setIsEditing(true);
    setEditContent(node.content);
  };
  
  // 保存编辑
  const handleSaveEdit = () => {
    if (editContent.trim() !== node.content) {
      dispatch({
        type: 'UPDATE_NODE_CONTENT',
        payload: { nodeId: node.id, content: editContent.trim() },
      });
    }
    setIsEditing(false);
  };
  
  // 取消编辑
  const handleCancelEdit = () => {
    setEditContent(node.content);
    setIsEditing(false);
  };
  
  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };
  
  // 处理点击导航
  const handleCellClick = () => {
    if (isEditing) return;
    
    if (state.isEditMode) {
      handleStartEdit();
    } else if (canNavigate && onNavigate) {
      onNavigate(node.id);
    }
  };
  
  // 处理AI生成
  const handleAIClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onAIGenerate) {
      onAIGenerate(node.id);
    }
  };
  
  // 获取单元格样式类
  const getCellClasses = () => {
    const baseClasses = 'mandala-cell';
    const classes = [baseClasses];
    
    if (isCenter) {
      classes.push('center');
    }
    
    if (state.isEditMode) {
      classes.push('editable');
    } else {
      classes.push('readonly');
    }
    
    if (canNavigate && !state.isEditMode) {
      classes.push('hover:bg-blue-50');
    }
    
    if (node.content.trim() === '') {
      classes.push('border-dashed');
    }
    
    return classes.join(' ') + ' ' + className;
  };
  
  return (
    <div
      className={getCellClasses()}
      style={style}
      onClick={handleCellClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      title={`${getLevelName(node.level)} - ${getPositionName(node.position)}`}
    >
      {/* AI生成按钮 */}
      {showAIButton && (isHovered || node.content.trim() === '') && (
        <button
          className="ai-button animate-scale-in"
          onClick={handleAIClick}
          title="AI生成内容"
        >
          <Sparkles size={12} />
        </button>
      )}
      
      {/* 内容显示/编辑 */}
      {isEditing ? (
        <textarea
          ref={textareaRef}
          value={editContent}
          onChange={(e) => setEditContent(e.target.value)}
          onBlur={handleSaveEdit}
          onKeyDown={handleKeyDown}
          className="w-full h-full resize-none border-none outline-none bg-transparent text-sm"
          placeholder={`输入${getLevelName(node.level)}...`}
          autoFocus
        />
      ) : (
        <div className="w-full h-full flex items-center justify-center text-center">
          {node.content.trim() === '' ? (
            <span className="text-gray-400 text-sm">
              {state.isEditMode ? `点击输入${getLevelName(node.level)}` : ''}
            </span>
          ) : (
            <span className="text-sm leading-tight break-words">
              {node.content}
            </span>
          )}
        </div>
      )}
      
      {/* 导航指示器 */}
      {canNavigate && !state.isEditMode && node.content.trim() !== '' && (
        <div className="absolute bottom-1 right-1 w-2 h-2 bg-blue-400 rounded-full opacity-60" />
      )}
    </div>
  );
};
