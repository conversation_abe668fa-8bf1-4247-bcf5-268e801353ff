import { AIGenerationRequest, AIGenerationResponse, MandalaNode } from '../types/mandala';
import { getLevelName } from '../utils/mandala';

// AI提示词模板
const AI_PROMPTS = {
  // 第2层：维度生成
  DIMENSION: `你是一个专业的思维导图专家。请基于给定的主题，生成8个不同的思考维度。

主题：{theme}

要求：
1. 生成8个不同的思考维度，每个维度用简洁的词语或短语表达（不超过10个字）
2. 维度要全面覆盖主题的各个方面
3. 维度之间要有逻辑关联但不重复
4. 适合进一步展开成具体的子主题
5. 按照重要性排序

请直接返回8个维度，每行一个，不需要编号：`,

  // 第3层：子主题生成
  SUBTOPIC: `你是一个专业的思维导图专家。请基于给定的维度，生成8个相关的子主题。

主题：{theme}
维度：{dimension}
上下文：{context}

要求：
1. 生成8个与该维度密切相关的子主题
2. 每个子主题用简洁明确的表达（不超过15个字）
3. 子主题要具体可操作，能够进一步分解为执行步骤
4. 覆盖该维度的不同方面
5. 按照重要性和逻辑顺序排列

请直接返回8个子主题，每行一个，不需要编号：`,

  // 第4层：执行步骤生成
  ACTION: `你是一个专业的项目管理专家。请基于给定的子主题，生成8个具体的执行步骤。

主题：{theme}
维度：{dimension}
子主题：{subtopic}
上下文：{context}

要求：
1. 生成8个具体可执行的行动步骤
2. 每个步骤要明确、具体、可衡量
3. 步骤之间要有逻辑顺序
4. 每个步骤用动词开头，表达清晰（不超过20个字）
5. 确保步骤的可操作性和实用性

请直接返回8个执行步骤，每行一个，不需要编号：`,

  // 单个内容生成
  SINGLE: `你是一个专业的思维导图专家。请基于给定的上下文，为指定位置生成一个合适的内容。

主题：{theme}
当前层级：{level}
位置：{position}
上下文：{context}

要求：
1. 生成一个与上下文相关的{level}内容
2. 内容要简洁明确，符合该层级的特点
3. 与其他内容形成良好的逻辑关联
4. 具有实用性和可操作性

请直接返回生成的内容，不需要额外说明：`,
};

import { aiServiceManager } from './aiProviders/aiServiceManager';

// 兼容性包装器 - 保持原有接口不变
class AIService {
  constructor() {
    // 初始化时从环境变量获取API密钥
    const envApiKey = import.meta.env.VITE_OPENAI_API_KEY || '';
    if (envApiKey) {
      aiServiceManager.updateConfig({ apiKey: envApiKey });
    }
  }

  // 设置API密钥
  setApiKey(apiKey: string) {
    aiServiceManager.updateConfig({ apiKey });
  }

  // 设置基础URL
  setBaseURL(baseURL: string) {
    aiServiceManager.updateConfig({ baseURL });
  }

  // 设置模型
  setModel(model: string) {
    aiServiceManager.updateConfig({ model });
  }

  // 设置温度
  setTemperature(temperature: number) {
    aiServiceManager.updateConfig({ temperature });
  }

  // 设置最大令牌数
  setMaxTokens(maxTokens: number) {
    aiServiceManager.updateConfig({ maxTokens });
  }

  // 获取当前配置（用于测试时备份）
  getCurrentConfig() {
    return aiServiceManager.getConfig();
  }

  // 测试API连接
  async testConnection(): Promise<{ success: boolean; message: string }> {
    return aiServiceManager.testConnection();
  }
  
  // 生成单个内容
  async generateSingleContent(request: AIGenerationRequest): Promise<string> {
    return aiServiceManager.generateSingleContent(request);
  }
  
  // 批量生成内容（生成8个子项）
  async generateBatchContent(request: AIGenerationRequest): Promise<string[]> {
    return aiServiceManager.generateBatchContent(request);
  }

}

// 导出单例
export const aiService = new AIService();
