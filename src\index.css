@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基础样式 */
* {
  border-color: #e5e7eb;
}

body {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
  color: #1f2937;
  font-feature-settings: "rlig" 1, "calt" 1;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 组件样式 */
.mandala-grid {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 0.5rem;
  aspect-ratio: 1 / 1;
  width: 100%;
  max-width: 32rem;
  margin: 0 auto;
}

.mandala-cell {
  position: relative;
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
  background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
  transform: translateZ(0);
}

.mandala-cell:hover {
  border-color: #60a5fa;
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
  transform: translateY(-2px) scale(1.02);
}

.mandala-cell.center {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
  border-color: #60a5fa;
  font-weight: 600;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.mandala-cell.editable {
  background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 100%);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.mandala-cell.readonly {
  background: linear-gradient(135deg, rgba(249,250,251,0.9) 0%, rgba(243,244,246,0.8) 100%);
}

.mandala-cell.border-dashed {
  border-style: dashed;
  border-color: #d1d5db;
}

.ai-button {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  width: 1.75rem;
  height: 1.75rem;
  background: linear-gradient(to right, #8b5cf6, #ec4899);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: scale(0.8);
  border: none;
}

.mandala-cell:hover .ai-button {
  opacity: 1;
  transform: scale(1);
}

.ai-button:hover {
  background: linear-gradient(to right, #7c3aed, #db2777);
  transform: scale(1.1);
  box-shadow: 0 4px 20px rgba(168, 85, 247, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mandala-grid {
    gap: 0.25rem;
    max-width: 24rem;
  }

  .mandala-cell {
    padding: 0.25rem;
    font-size: 0.75rem;
    min-height: 60px;
  }

  .ai-button {
    width: 1.25rem;
    height: 1.25rem;
    top: -0.25rem;
    right: -0.25rem;
  }
}

@media (max-width: 480px) {
  .mandala-grid {
    gap: 0.25rem;
    max-width: 20rem;
  }

  .mandala-cell {
    padding: 0.25rem;
    font-size: 0.75rem;
    min-height: 50px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1024px) {
  .mandala-grid {
    gap: 1rem;
    max-width: 56rem;
  }

  .mandala-cell {
    padding: 1rem;
    min-height: 120px;
  }
}

/* 动画定义 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 工具类 */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(to right, #3b82f6, #2563eb);
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.btn-secondary {
  background: white;
  color: #374151;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  cursor: pointer;
}

.btn-secondary:hover {
  background: #f9fafb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.btn-ghost {
  background: transparent;
  color: #6b7280;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-ghost:hover {
  background: #f3f4f6;
}

/* 卡片样式 */
.card {
  background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid #f3f4f6;
  backdrop-filter: blur(4px);
}

/* 导航栏样式 */
.navbar {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  animation: fadeIn 0.2s ease-out;
}

.dialog-content {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid #f3f4f6;
  max-width: 28rem;
  width: 100%;
  margin: 0 1rem;
  animation: slideIn 0.3s ease-out;
}

/* 文本样式 */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
