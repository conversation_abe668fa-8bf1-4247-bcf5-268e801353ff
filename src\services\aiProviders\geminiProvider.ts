import {
  AIProvider,
  AIGenerationRequest,
  AIGenerationResponse,
  AITestConnectionResult,
  AIProviderConfig
} from './types';

// 自定义Provider，兼容OpenAI格式
export class CustomProvider extends AIProvider {
  constructor(config: AIProviderConfig) {
    super(config);
  }

  getProviderName(): string {
    return '自定义API';
  }

  getSupportedModels(): string[] {
    return []; // 自定义模型由用户配置
  }

  async testConnection(): Promise<AITestConnectionResult> {
    const startTime = Date.now();

    try {
      const response = await fetch(`${this.config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        body: JSON.stringify({
          model: this.config.model,
          messages: [
            {
              role: 'user',
              content: '测试连接，请回复"连接成功"'
            }
          ],
          max_tokens: 10,
          temperature: 0
        })
      });

      const latency = Date.now() - startTime;

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `HTTP ${response.status}`;

        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error?.message || errorMessage;
        } catch {
          // 如果不是JSON，使用状态码
        }

        return {
          success: false,
          message: `API请求失败: ${errorMessage}`,
          latency
        };
      }

      const data = await response.json();

      if (data.choices && data.choices.length > 0) {
        return {
          success: true,
          message: '自定义API连接测试成功！',
          latency
        };
      } else {
        return {
          success: false,
          message: 'API返回格式异常',
          latency
        };
      }
    } catch (error) {
      const latency = Date.now() - startTime;

      if (error instanceof TypeError && error.message.includes('fetch')) {
        return {
          success: false,
          message: '网络连接失败，请检查URL是否正确',
          latency
        };
      }

      return {
        success: false,
        message: `连接失败: ${error instanceof Error ? error.message : '未知错误'}`,
        latency
      };
    }
  }

  async generateResponse(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    if (!this.config.apiKey.trim()) {
      throw new Error('请先配置API密钥');
    }

    try {
      const response = await fetch(`${this.config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        body: JSON.stringify({
          model: request.model || this.config.model,
          messages: request.messages.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          temperature: request.temperature ?? this.config.temperature,
          max_tokens: request.maxTokens ?? this.config.maxTokens
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `HTTP ${response.status}`;

        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error?.message || errorMessage;
        } catch {
          // 如果不是JSON，使用状态码
        }

        throw new Error(`自定义API错误: ${errorMessage}`);
      }

      const data = await response.json();

      if (!data.choices || data.choices.length === 0) {
        throw new Error('API返回格式异常：缺少choices字段');
      }

      const choice = data.choices[0];

      return {
        content: choice.message?.content || '',
        usage: data.usage ? {
          inputTokens: data.usage.prompt_tokens || 0,
          outputTokens: data.usage.completion_tokens || 0,
          totalTokens: data.usage.total_tokens || 0
        } : undefined,
        finishReason: choice.finish_reason
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`自定义API调用失败: ${String(error)}`);
    }
  }
}

// Gemini API请求格式
interface GeminiContent {
  role?: 'user' | 'model';
  parts: Array<{
    text: string;
  }>;
}

interface GeminiRequest {
  contents: GeminiContent[];
  generationConfig?: {
    temperature?: number;
    maxOutputTokens?: number;
    candidateCount?: number;
  };
  systemInstruction?: {
    parts: Array<{
      text: string;
    }>;
  };
}

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
      role: string;
    };
    finishReason: string;
    index: number;
  }>;
  usageMetadata: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

interface GeminiErrorResponse {
  error: {
    code: number;
    message: string;
    status: string;
  };
}

export class GeminiProvider extends AIProvider {
  constructor(config: AIProviderConfig) {
    super(config);
  }

  getProviderName(): string {
    return 'Google Gemini';
  }

  getSupportedModels(): string[] {
    return [
      'gemini-pro',
      'gemini-pro-vision',
      'gemini-1.5-pro',
      'gemini-1.5-flash',
      'gemini-2.0-flash-exp'
    ];
  }

  private getApiUrl(model: string): string {
    return `${this.config.baseURL}/models/${model}:generateContent`;
  }

  async testConnection(): Promise<AITestConnectionResult> {
    const startTime = Date.now();
    
    try {
      const url = `${this.getApiUrl(this.config.model)}?key=${this.config.apiKey}`;
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: '测试连接，请回复"连接成功"'
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0,
            maxOutputTokens: 10,
            candidateCount: 1
          }
        } as GeminiRequest)
      });

      const latency = Date.now() - startTime;

      if (!response.ok) {
        const errorData: GeminiErrorResponse = await response.json();
        return {
          success: false,
          message: `API请求失败 (${response.status}): ${errorData.error?.message || response.statusText}`,
          latency
        };
      }

      const data: GeminiResponse = await response.json();
      
      if (data.candidates && data.candidates.length > 0) {
        return {
          success: true,
          message: 'Google Gemini API连接测试成功！',
          latency
        };
      } else {
        return {
          success: false,
          message: 'API返回格式异常：缺少candidates字段',
          latency
        };
      }
    } catch (error) {
      const latency = Date.now() - startTime;
      
      if (error instanceof TypeError && error.message.includes('fetch')) {
        return {
          success: false,
          message: '网络连接失败，请检查URL是否正确',
          latency
        };
      }
      
      return {
        success: false,
        message: `连接失败: ${error instanceof Error ? error.message : '未知错误'}`,
        latency
      };
    }
  }

  async generateResponse(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    if (!this.config.apiKey.trim()) {
      throw new Error('请先配置Google Gemini API密钥');
    }

    // 分离系统消息和用户/助手消息
    let systemInstruction: { parts: Array<{ text: string }> } | undefined;
    const contents: GeminiContent[] = [];
    
    for (const msg of request.messages) {
      if (msg.role === 'system') {
        systemInstruction = {
          parts: [{ text: msg.content }]
        };
      } else {
        contents.push({
          role: msg.role === 'assistant' ? 'model' : 'user',
          parts: [{ text: msg.content }]
        });
      }
    }

    const geminiRequest: GeminiRequest = {
      contents,
      generationConfig: {
        temperature: request.temperature ?? this.config.temperature,
        maxOutputTokens: request.maxTokens ?? this.config.maxTokens,
        candidateCount: 1
      }
    };

    // 如果有系统指令，添加到请求中
    if (systemInstruction) {
      geminiRequest.systemInstruction = systemInstruction;
    }

    try {
      const model = request.model || this.config.model;
      const url = `${this.getApiUrl(model)}?key=${this.config.apiKey}`;
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(geminiRequest)
      });

      if (!response.ok) {
        const errorData: GeminiErrorResponse = await response.json();
        throw new Error(`Gemini API错误 (${response.status}): ${errorData.error?.message || response.statusText}`);
      }

      const data: GeminiResponse = await response.json();
      
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('Gemini API返回格式异常：缺少candidates字段');
      }

      const candidate = data.candidates[0];
      
      if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
        throw new Error('Gemini API返回格式异常：缺少content.parts字段');
      }

      // 提取文本内容
      const textContent = candidate.content.parts
        .map(part => part.text)
        .join('');
      
      return {
        content: textContent,
        usage: {
          inputTokens: data.usageMetadata.promptTokenCount,
          outputTokens: data.usageMetadata.candidatesTokenCount,
          totalTokens: data.usageMetadata.totalTokenCount
        },
        finishReason: candidate.finishReason
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`Gemini API调用失败: ${String(error)}`);
    }
  }
}
