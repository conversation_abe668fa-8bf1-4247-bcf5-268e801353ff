import { MandalaChart } from '../types/mandala';

// 存储键名
const STORAGE_KEYS = {
  CHARTS: 'mandala_charts',
  CURRENT_CHART: 'mandala_current_chart',
  APP_SETTINGS: 'mandala_app_settings',
  AUTO_SAVE: 'mandala_auto_save',
} as const;

// 应用设置接口
interface AppSettings {
  autoSave: boolean;
  autoSaveInterval: number; // 秒
  theme: 'light' | 'dark' | 'auto';
  language: 'zh' | 'en';
  aiApiKey?: string;
  aiProvider?: string;
  aiBaseURL?: string;
  aiModel?: string;
  aiTemperature?: number;
  aiMaxTokens?: number;
}

// 默认设置
const DEFAULT_SETTINGS: AppSettings = {
  autoSave: true,
  autoSaveInterval: 30,
  theme: 'light',
  language: 'zh',
  aiProvider: 'openai',
  aiBaseURL: 'https://api.openai.com/v1',
  aiModel: 'gpt-3.5-turbo',
  aiTemperature: 0.7,
  aiMaxTokens: 500,
};

class StorageService {
  private isAvailable: boolean;
  
  constructor() {
    this.isAvailable = this.checkStorageAvailability();
  }
  
  // 检查localStorage是否可用
  private checkStorageAvailability(): boolean {
    try {
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }
  
  // 安全的JSON解析
  private safeJsonParse<T>(json: string, fallback: T): T {
    try {
      return JSON.parse(json);
    } catch {
      return fallback;
    }
  }
  
  // 保存曼陀罗图
  saveChart(chart: MandalaChart): boolean {
    if (!this.isAvailable) return false;
    
    try {
      // 获取现有图表列表
      const charts = this.getAllCharts();
      
      // 更新或添加图表
      const existingIndex = charts.findIndex(c => c.id === chart.id);
      if (existingIndex >= 0) {
        charts[existingIndex] = chart;
      } else {
        charts.push(chart);
      }
      
      // 保存图表列表
      localStorage.setItem(STORAGE_KEYS.CHARTS, JSON.stringify(charts));
      
      // 保存为当前图表
      localStorage.setItem(STORAGE_KEYS.CURRENT_CHART, JSON.stringify(chart));
      
      return true;
    } catch (error) {
      console.error('保存图表失败:', error);
      return false;
    }
  }
  
  // 获取所有图表
  getAllCharts(): MandalaChart[] {
    if (!this.isAvailable) return [];
    
    try {
      const chartsJson = localStorage.getItem(STORAGE_KEYS.CHARTS);
      return chartsJson ? this.safeJsonParse(chartsJson, []) : [];
    } catch (error) {
      console.error('获取图表列表失败:', error);
      return [];
    }
  }
  
  // 获取当前图表
  getCurrentChart(): MandalaChart | null {
    if (!this.isAvailable) return null;
    
    try {
      const chartJson = localStorage.getItem(STORAGE_KEYS.CURRENT_CHART);
      return chartJson ? this.safeJsonParse(chartJson, null) : null;
    } catch (error) {
      console.error('获取当前图表失败:', error);
      return null;
    }
  }
  
  // 根据ID获取图表
  getChartById(id: string): MandalaChart | null {
    const charts = this.getAllCharts();
    return charts.find(chart => chart.id === id) || null;
  }
  
  // 删除图表
  deleteChart(id: string): boolean {
    if (!this.isAvailable) return false;
    
    try {
      const charts = this.getAllCharts();
      const filteredCharts = charts.filter(chart => chart.id !== id);
      
      localStorage.setItem(STORAGE_KEYS.CHARTS, JSON.stringify(filteredCharts));
      
      // 如果删除的是当前图表，清除当前图表
      const currentChart = this.getCurrentChart();
      if (currentChart && currentChart.id === id) {
        localStorage.removeItem(STORAGE_KEYS.CURRENT_CHART);
      }
      
      return true;
    } catch (error) {
      console.error('删除图表失败:', error);
      return false;
    }
  }
  
  // 保存应用设置
  saveSettings(settings: Partial<AppSettings>): boolean {
    if (!this.isAvailable) return false;
    
    try {
      const currentSettings = this.getSettings();
      const newSettings = { ...currentSettings, ...settings };
      localStorage.setItem(STORAGE_KEYS.APP_SETTINGS, JSON.stringify(newSettings));
      return true;
    } catch (error) {
      console.error('保存设置失败:', error);
      return false;
    }
  }
  
  // 获取应用设置
  getSettings(): AppSettings {
    if (!this.isAvailable) return DEFAULT_SETTINGS;
    
    try {
      const settingsJson = localStorage.getItem(STORAGE_KEYS.APP_SETTINGS);
      const settings = settingsJson ? this.safeJsonParse(settingsJson, DEFAULT_SETTINGS) : DEFAULT_SETTINGS;
      return { ...DEFAULT_SETTINGS, ...settings };
    } catch (error) {
      console.error('获取设置失败:', error);
      return DEFAULT_SETTINGS;
    }
  }
  
  // 自动保存
  enableAutoSave(chart: MandalaChart, interval: number = 30): number | null {
    if (!this.isAvailable) return null;
    
    return window.setInterval(() => {
      this.saveChart(chart);
      console.log('自动保存完成');
    }, interval * 1000);
  }
  
  // 停止自动保存
  disableAutoSave(intervalId: number): void {
    if (intervalId) {
      clearInterval(intervalId);
    }
  }
  
  // 导出数据
  exportData(): string {
    const data = {
      charts: this.getAllCharts(),
      settings: this.getSettings(),
      exportTime: new Date().toISOString(),
      version: '1.0.0',
    };
    
    return JSON.stringify(data, null, 2);
  }
  
  // 导入数据
  importData(jsonData: string): boolean {
    if (!this.isAvailable) return false;
    
    try {
      const data = JSON.parse(jsonData);
      
      // 验证数据格式
      if (!data.charts || !Array.isArray(data.charts)) {
        throw new Error('无效的数据格式');
      }
      
      // 导入图表
      localStorage.setItem(STORAGE_KEYS.CHARTS, JSON.stringify(data.charts));
      
      // 导入设置
      if (data.settings) {
        localStorage.setItem(STORAGE_KEYS.APP_SETTINGS, JSON.stringify(data.settings));
      }
      
      return true;
    } catch (error) {
      console.error('导入数据失败:', error);
      return false;
    }
  }
  
  // 清除所有数据
  clearAllData(): boolean {
    if (!this.isAvailable) return false;
    
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
      return true;
    } catch (error) {
      console.error('清除数据失败:', error);
      return false;
    }
  }
  
  // 获取存储使用情况
  getStorageInfo(): { used: number; available: boolean } {
    if (!this.isAvailable) {
      return { used: 0, available: false };
    }
    
    try {
      let used = 0;
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length;
        }
      }
      
      return { used, available: true };
    } catch {
      return { used: 0, available: false };
    }
  }
}

// 导出单例
export const storageService = new StorageService();
